from typing import List, Optional
from .data_models import StockData, TradingSignal
from decimal import Decimal

class SignalGenerator:
    """交易信号生成器"""
    
    def __init__(self, macd_divergence_threshold=0.1, bb_reversal_threshold=0.05):
        self.macd_divergence_threshold = Decimal(str(macd_divergence_threshold))
        self.bb_reversal_threshold = Decimal(str(bb_reversal_threshold))
    
    def generate_signals(self, stock_data: List[StockData]) -> List[TradingSignal]:
        """生成交易信号"""
        signals = []
        
        for i in range(2, len(stock_data)):
            current = stock_data[i]
            prev = stock_data[i-1]
            prev2 = stock_data[i-2]
            
            # MACD背离信号
            macd_signal = self._check_macd_divergence(current, prev, prev2)
            if macd_signal:
                signals.append(macd_signal)
            
            # 布林线折返信号
            bb_signal = self._check_bollinger_reversal(current, prev)
            if bb_signal:
                signals.append(bb_signal)
        
        return signals
    
    def _check_macd_divergence(self, current: StockData, prev: StockData, prev2: StockData) -> Optional[TradingSignal]:
        """检查MACD背离"""
        if not all([current.macd, prev.macd, prev2.macd, 
                   current.macd_histogram, prev.macd_histogram]):
            return None
        
        # 价格创新高，MACD未创新高（顶背离）
        if (current.close > prev.close > prev2.close and 
            current.macd < prev.macd and 
            current.macd_histogram < prev.macd_histogram):
            return TradingSignal(
                symbol=current.symbol,
                date=current.date,
                signal_type='SELL',
                price=current.close,
                reason='MACD顶背离',
                confidence=0.8
            )
        
        # 价格创新低，MACD未创新低（底背离）
        if (current.close < prev.close < prev2.close and 
            current.macd > prev.macd and 
            current.macd_histogram > prev.macd_histogram):
            return TradingSignal(
                symbol=current.symbol,
                date=current.date,
                signal_type='BUY',
                price=current.close,
                reason='MACD底背离',
                confidence=0.8
            )
        
        return None
    
    def _check_bollinger_reversal(self, current: StockData, prev: StockData) -> Optional[TradingSignal]:
        """检查布林线折返"""
        if not all([current.bb_upper, current.bb_lower, current.close, prev.close]):
            return None
        
        # 价格触及上轨后回落
        if (prev.close >= current.bb_upper * (Decimal(1) - self.bb_reversal_threshold) and 
            current.close < prev.close):
            return TradingSignal(
                symbol=current.symbol,
                date=current.date,
                signal_type='SELL',
                price=current.close,
                reason='布林上轨折返',
                confidence=0.7
            )
        
        # 价格触及下轨后反弹
        if (prev.close <= current.bb_lower * (Decimal(1) + self.bb_reversal_threshold) and 
            current.close > prev.close):
            return TradingSignal(
                symbol=current.symbol,
                date=current.date,
                signal_type='BUY',
                price=current.close,
                reason='布林下轨折返',
                confidence=0.7
            )
        
        return None